"""
Supabase client for the backend.

This module provides a Supabase client for the backend.
"""

from typing import Any, Dict, List, Optional


class MockResult:
    """Mock result object that mimics Supabase query results."""

    def __init__(self, data: List[Dict[str, Any]], error: Optional[str] = None, count: int = 0):
        self.data = data
        self.error = error
        self.count = count


class SupabaseClient:
    """Mock Supabase client for testing."""

    def __init__(self):
        """Initialize the Supabase client."""
        self.current_table = None
        self.current_query = {}
        self.current_schema = None

    def schema(self, schema_name: str):
        """
        Set the schema for the query.

        Args:
            schema_name: The name of the schema

        Returns:
            self: The Supabase client
        """
        self.current_schema = schema_name
        return self

    def from_(self, table: str):
        """Set the table for the query (alternative to table method)."""
        self.current_table = table
        return self

    def table(self, table_name: str):
        """
        Select a table to query.

        Args:
            table_name: The name of the table

        Returns:
            self: The Supabase client
        """
        self.current_table = table_name
        self.current_query = {}
        return self

    def select(self, columns: str = "*"):
        """
        Select columns from the table.

        Args:
            columns: The columns to select

        Returns:
            self: The Supabase client
        """
        self.current_query["select"] = columns
        return self

    def eq(self, column: str, value: Any):
        """
        Add an equality filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append({"type": "eq", "column": column, "value": value})
        return self

    def is_(self, column: str, value: Any):
        """
        Add an IS filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append({"type": "is", "column": column, "value": value})
        return self

    def lte(self, column: str, value: Any):
        """
        Add a less than or equal filter to the query.

        Args:
            column: The column to filter on
            value: The value to filter for

        Returns:
            self: The Supabase client
        """
        if "filters" not in self.current_query:
            self.current_query["filters"] = []

        self.current_query["filters"].append({"type": "lte", "column": column, "value": value})
        return self

    def order(self, column: str, desc: bool = False):
        """
        Add an order by clause to the query.

        Args:
            column: The column to order by
            desc: Whether to order in descending order

        Returns:
            self: The Supabase client
        """
        self.current_query["order"] = {"column": column, "desc": desc}
        return self

    def limit(self, limit: int):
        """
        Add a limit clause to the query.

        Args:
            limit: The maximum number of rows to return

        Returns:
            self: The Supabase client
        """
        self.current_query["limit"] = limit
        return self

    def update(self, data: Dict[str, Any]):
        """
        Update rows in the table.

        Args:
            data: The data to update

        Returns:
            self: The Supabase client
        """
        self.current_query["update"] = data
        return self

    def execute(self):
        """
        Execute the query.

        Returns:
            MockResult: The query result
        """
        # This is a mock implementation that returns empty data
        return MockResult(data=[], error=None, count=0)


def get_supabase_client(use_service_role: bool = False) -> SupabaseClient:
    """
    Get a Supabase client (sync version).

    Args:
        use_service_role: Whether to use service role credentials (ignored in mock)

    Returns:
        SupabaseClient: The Supabase client
    """
    return SupabaseClient()


async def get_supabase_client_async(use_service_role: bool = False) -> SupabaseClient:
    """
    Get a Supabase client (async version).

    Args:
        use_service_role: Whether to use service role credentials (ignored in mock)

    Returns:
        SupabaseClient: The Supabase client
    """
    return SupabaseClient()
