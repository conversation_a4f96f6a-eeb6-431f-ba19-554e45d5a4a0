"""
Test suite for Deadline Insights API endpoints.

This module tests all the deadline insights API endpoints to ensure
they work correctly with proper authentication and data handling.
"""

import pytest
import json
from datetime import datetime, timezone, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Import the FastAPI app
from backend.api.main import create_app

# Create test client
app = create_app()
client = TestClient(app)


class TestDeadlineInsightsAPI:
    """Test class for deadline insights API endpoints."""
    
    @pytest.fixture
    def mock_auth(self):
        """Mock authentication dependencies."""
        with patch('backend.api.dependencies.auth.get_current_user') as mock_user, \
             patch('backend.api.dependencies.auth.get_current_tenant') as mock_tenant:
            
            mock_user.return_value = {
                "id": "test-user-123",
                "email": "<EMAIL>",
                "first_name": "Test"
            }
            mock_tenant.return_value = {
                "id": "test-tenant-123",
                "name": "Test Tenant"
            }
            yield mock_user, mock_tenant
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client."""
        with patch('backend.db.supabase_client.get_supabase_client') as mock_client:
            mock_supabase = MagicMock()
            mock_client.return_value = mock_supabase
            yield mock_supabase
    
    def test_get_deadline_insights_summary_success(self, mock_auth, mock_supabase):
        """Test successful retrieval of deadline insights summary."""
        mock_user, mock_tenant = mock_auth
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": [
                        {"priority": "CRITICAL"},
                        {"priority": "HIGH"},
                        {"priority": "MEDIUM"}
                    ],
                    "conflicts": [{"type": "scheduling"}]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/summary")
        
        assert response.status_code == 200
        data = response.json()
        assert "critical_deadlines" in data
        assert "high_risk_deadlines" in data
        assert "conflicts_detected" in data
        assert "recommendations" in data
        assert data["critical_deadlines"] == 1
        assert data["high_risk_deadlines"] == 1
        assert data["conflicts_detected"] == 1
        assert data["recommendations"] == 3
    
    def test_get_deadline_insights_summary_no_data(self, mock_auth, mock_supabase):
        """Test summary endpoint when no insights data exists."""
        mock_user, mock_tenant = mock_auth
        
        # Mock empty Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = []
        
        response = client.get("/deadline-insights/summary")
        
        assert response.status_code == 200
        data = response.json()
        assert data["critical_deadlines"] == 0
        assert data["high_risk_deadlines"] == 0
        assert data["conflicts_detected"] == 0
        assert data["recommendations"] == 0
    
    def test_get_detailed_deadline_insights_success(self, mock_auth, mock_supabase):
        """Test successful retrieval of detailed deadline insights."""
        mock_user, mock_tenant = mock_auth
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.eq.return_value.gte.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "analysis_type": "comprehensive",
                "insights": {
                    "recommendations": [],
                    "conflicts": [],
                    "risk_assessment": "low"
                },
                "generated_at": "2025-07-12T10:00:00Z",
                "expires_at": "2025-07-12T14:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/detailed")
        
        assert response.status_code == 200
        data = response.json()
        assert "tenant_id" in data
        assert "analysis_type" in data
        assert "insights" in data
        assert "generated_at" in data
        assert "expires_at" in data
        assert data["cache_hit"] == True
    
    def test_get_critical_deadlines_success(self, mock_auth, mock_supabase):
        """Test successful retrieval of critical deadlines."""
        mock_user, mock_tenant = mock_auth
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": [
                        {
                            "id": "rec-1",
                            "title": "Critical Deadline",
                            "priority": "CRITICAL",
                            "due_date": "2025-07-13"
                        },
                        {
                            "id": "rec-2", 
                            "title": "High Priority Task",
                            "priority": "HIGH",
                            "due_date": "2025-07-14"
                        }
                    ]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/critical?limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert "critical_deadlines" in data
        assert "total_count" in data
        assert len(data["critical_deadlines"]) == 2
        assert data["critical_deadlines"][0]["priority"] == "CRITICAL"
    
    def test_get_deadline_conflicts_success(self, mock_auth, mock_supabase):
        """Test successful retrieval of deadline conflicts."""
        mock_user, mock_tenant = mock_auth
        
        # Mock Supabase response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "conflicts": [
                        {
                            "id": "conflict-1",
                            "type": "scheduling",
                            "description": "Two deadlines on same day",
                            "severity": "high"
                        }
                    ]
                },
                "generated_at": "2025-07-12T10:00:00Z"
            }
        ]
        
        response = client.get("/deadline-insights/conflicts")
        
        assert response.status_code == 200
        data = response.json()
        assert "conflicts" in data
        assert "total_count" in data
        assert len(data["conflicts"]) == 1
        assert data["conflicts"][0]["type"] == "scheduling"
    
    @patch('jobs.helpers.submit_deadline_insights_job')
    def test_trigger_deadline_insights_success(self, mock_submit_job, mock_auth, mock_supabase):
        """Test successful triggering of deadline insights generation."""
        mock_user, mock_tenant = mock_auth
        mock_submit_job.return_value = "job-123"
        
        # Mock recent insights check (no recent insights)
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.execute.return_value.data = []
        
        request_data = {
            "analysis_type": "comprehensive",
            "force_refresh": False
        }
        
        response = client.post("/deadline-insights/trigger", json=request_data)
        
        assert response.status_code == 202
        data = response.json()
        assert "job_id" in data
        assert data["job_id"] == "job-123"
        assert "message" in data
        mock_submit_job.assert_called_once()
    
    def test_trigger_deadline_insights_recent_generation(self, mock_auth, mock_supabase):
        """Test triggering insights when recent generation exists."""
        mock_user, mock_tenant = mock_auth
        
        # Mock recent insights check (recent insights exist)
        recent_time = datetime.now(timezone.utc) - timedelta(minutes=10)
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.execute.return_value.data = [
            {"generated_at": recent_time.isoformat()}
        ]
        
        request_data = {
            "analysis_type": "comprehensive",
            "force_refresh": False
        }
        
        response = client.post("/deadline-insights/trigger", json=request_data)
        
        assert response.status_code == 202
        data = response.json()
        assert "message" in data
        assert "recently generated" in data["message"].lower()
    
    def test_get_morning_briefing_success(self, mock_auth, mock_supabase):
        """Test successful retrieval of morning briefing."""
        mock_user, mock_tenant = mock_auth
        
        # Mock deadlines response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.lte.return_value.eq.return_value.execute.return_value.data = [
            {
                "id": "deadline-1",
                "title": "Critical Filing",
                "priority": "critical",
                "due_date": "2025-07-12T17:00:00Z"
            }
        ]
        
        # Mock tasks response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.gte.return_value.lte.return_value.neq.return_value.execute.return_value.data = [
            {
                "id": "task-1",
                "title": "Review documents",
                "priority": "high"
            }
        ]
        
        # Mock insights response
        mock_supabase.schema.return_value.from_.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = [
            {
                "insights": {
                    "recommendations": []
                }
            }
        ]
        
        response = client.get("/deadline-insights/morning-briefing")
        
        assert response.status_code == 200
        data = response.json()
        assert "greeting" in data
        assert "today_summary" in data
        assert "priority_actions" in data
        assert "insights" in data
        assert "weather_check" in data
        assert data["today_summary"]["critical_deadlines"] == 1
    
    def test_unauthorized_access(self):
        """Test that endpoints require authentication."""
        with patch('backend.api.dependencies.auth.get_current_user') as mock_user:
            mock_user.side_effect = Exception("Unauthorized")
            
            response = client.get("/deadline-insights/summary")
            assert response.status_code == 401  # FastAPI returns 401 for unauthorized access


class TestJobsAPI:
    """Test class for jobs API endpoints."""
    
    @pytest.fixture
    def mock_auth(self):
        """Mock authentication dependencies."""
        with patch('backend.api.dependencies.auth.get_current_user') as mock_user, \
             patch('backend.api.dependencies.auth.get_current_tenant') as mock_tenant:
            
            mock_user.return_value = {
                "id": "test-user-123",
                "email": "<EMAIL>"
            }
            mock_tenant.return_value = {
                "id": "test-tenant-123",
                "name": "Test Tenant"
            }
            yield mock_user, mock_tenant
    
    @patch('jobs.helpers.trigger_user_return_insights')
    def test_trigger_user_return_insights_success(self, mock_trigger, mock_auth):
        """Test successful triggering of user return insights."""
        mock_user, mock_tenant = mock_auth
        mock_trigger.return_value = "job-456"
        
        request_data = {
            "user_id": "test-user-123",
            "tenant_id": "test-tenant-123",
            "last_activity_time": "2025-07-12T08:00:00Z"
        }
        
        response = client.post("/jobs/user-return-insights", json=request_data)
        
        assert response.status_code == 202
        data = response.json()
        assert data["success"] == True
        assert data["job_id"] == "job-456"
        mock_trigger.assert_called_once()
    
    @patch('jobs.helpers.check_job_status')
    def test_get_job_status_success(self, mock_check_status, mock_auth):
        """Test successful job status retrieval."""
        mock_user, mock_tenant = mock_auth
        mock_check_status.return_value = {
            "job_id": "job-123",
            "status": "SUCCESS",
            "checked_at": "2025-07-12T10:00:00Z",
            "result": {"message": "Job completed successfully"}
        }
        
        response = client.get("/jobs/status/job-123")
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "job-123"
        assert data["status"] == "SUCCESS"
        assert data["result"]["message"] == "Job completed successfully"


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
